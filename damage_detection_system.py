import os
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image, ImageDraw, ImageFont
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib
import re
from scipy import ndimage
from skimage import measure, segmentation, filters
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DamageDetectionSystem:
    def __init__(self):
        self.classifier = None
        self.scaler = None
        self.class_names = ['剥落', '压伤', '擦伤', '点蚀', '磨损']
        self.colors = {
            '剥落': (255, 0, 0),    # 红色
            '压伤': (0, 255, 0),    # 绿色  
            '擦伤': (0, 0, 255),    # 蓝色
            '点蚀': (255, 255, 0),  # 黄色
            '磨损': (255, 0, 255)   # 紫色
        }
    
    def extract_features(self, image_patch):
        """提取图像块特征"""
        # 调整大小
        patch = cv2.resize(image_patch, (64, 64))
        gray = cv2.cvtColor(patch, cv2.COLOR_RGB2GRAY)
        
        features = []
        
        # 1. 统计特征
        features.extend([
            np.mean(gray), np.std(gray), np.median(gray),
            np.percentile(gray, 25), np.percentile(gray, 75)
        ])
        
        # 2. 纹理特征 - LBP简化版
        lbp = self._simple_lbp(gray)
        hist, _ = np.histogram(lbp.ravel(), bins=8, range=(0, 8))
        features.extend(hist / np.sum(hist))
        
        # 3. 边缘特征
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        features.append(edge_density)
        
        # 4. 梯度特征
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        grad_mag = np.sqrt(grad_x**2 + grad_y**2)
        features.extend([np.mean(grad_mag), np.std(grad_mag)])
        
        # 5. 颜色特征
        for channel in range(3):
            channel_data = patch[:, :, channel]
            features.extend([np.mean(channel_data), np.std(channel_data)])
        
        return np.array(features)
    
    def _simple_lbp(self, image):
        """简化的LBP实现"""
        rows, cols = image.shape
        lbp = np.zeros_like(image)
        
        for i in range(1, rows-1):
            for j in range(1, cols-1):
                center = image[i, j]
                code = 0
                code |= (image[i-1, j-1] >= center) << 7
                code |= (image[i-1, j] >= center) << 6
                code |= (image[i-1, j+1] >= center) << 5
                code |= (image[i, j+1] >= center) << 4
                code |= (image[i+1, j+1] >= center) << 3
                code |= (image[i+1, j] >= center) << 2
                code |= (image[i+1, j-1] >= center) << 1
                code |= (image[i, j-1] >= center) << 0
                lbp[i, j] = code
        
        return lbp
    
    def train_classifier(self, dataset_path):
        """训练分类器"""
        print("训练损伤类型分类器...")
        
        X, y = [], []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_path = os.path.join(dataset_path, class_name)
            if not os.path.exists(class_path):
                continue
                
            images = [f for f in os.listdir(class_path) 
                     if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
            
            print(f"处理类别 '{class_name}': {len(images)} 张图片")
            
            for img_file in images:
                img_path = os.path.join(class_path, img_file)
                try:
                    # 尝试多种方式加载TIFF图像
                    image = None

                    # 方法1: 使用PIL加载
                    try:
                        pil_img = Image.open(img_path)
                        image = np.array(pil_img.convert('RGB'))
                    except:
                        pass

                    # 方法2: 使用OpenCV加载
                    if image is None:
                        try:
                            image = cv2.imread(img_path, cv2.IMREAD_COLOR)
                            if image is not None:
                                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                        except:
                            pass

                    # 方法3: 使用OpenCV的TIFF标志
                    if image is None:
                        try:
                            image = cv2.imread(img_path, cv2.IMREAD_ANYCOLOR | cv2.IMREAD_ANYDEPTH)
                            if image is not None and len(image.shape) == 3:
                                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                            elif image is not None and len(image.shape) == 2:
                                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                        except:
                            pass

                    if image is None:
                        print(f"无法加载图片: {img_file}")
                        continue
                    
                    # 提取多个随机块进行训练
                    h, w = image.shape[:2]
                    for _ in range(10):  # 每张图片提取10个块
                        x = np.random.randint(0, max(1, w-64))
                        y = np.random.randint(0, max(1, h-64))
                        patch = image[y:y+64, x:x+64]
                        
                        if patch.shape[:2] == (64, 64):
                            features = self.extract_features(patch)
                            X.append(features)
                            y.append(class_idx)
                            
                except Exception as e:
                    print(f"处理图片 {img_file} 时出错: {e}")
        
        if len(X) == 0:
            print("错误: 没有成功加载任何图像数据")
            return 0.0

        X = np.array(X)
        y = np.array(y)
        
        print(f"训练数据: {len(X)} 个样本")
        
        # 训练模型
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        self.classifier = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        
        self.classifier.fit(X_train_scaled, y_train)
        
        # 评估
        y_pred = self.classifier.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        print(f"分类器准确率: {accuracy:.4f}")
        
        return accuracy
    
    def detect_scale_bar(self, image):
        """检测比例尺"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        h, w = gray.shape
        
        # 在图像底部区域寻找比例尺
        bottom_region = gray[int(h*0.8):, :]
        
        # 检测水平线段
        edges = cv2.Canny(bottom_region, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                               minLineLength=30, maxLineGap=5)
        
        scale_length_pixels = None
        scale_text = None
        
        if lines is not None:
            # 找到最长的水平线
            max_length = 0
            best_line = None
            
            for line in lines:
                x1, y1, x2, y2 = line[0]
                length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                angle = np.abs(np.arctan2(y2-y1, x2-x1) * 180 / np.pi)
                
                # 检查是否为水平线
                if angle < 10 or angle > 170:
                    if length > max_length:
                        max_length = length
                        best_line = line[0]
            
            if best_line is not None:
                scale_length_pixels = max_length
                
                # 尝试识别比例尺文字（简化版）
                # 在实际应用中，这里应该使用OCR
                scale_text = "100μm"  # 默认值，实际应该通过OCR识别
        
        return scale_length_pixels, scale_text
    
    def segment_damage_regions(self, image):
        """分割损伤区域"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # 1. 预处理
        # 高斯滤波去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 2. 多种分割方法组合
        
        # 方法1: Otsu阈值分割
        _, thresh1 = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 方法2: 自适应阈值
        thresh2 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
        
        # 方法3: 边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 组合结果
        combined = cv2.bitwise_or(thresh1, thresh2)
        combined = cv2.bitwise_or(combined, edges)
        
        # 3. 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
        combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel)
        
        # 4. 连通组件分析
        num_labels, labels = cv2.connectedComponents(combined)
        
        # 过滤小区域
        min_area = 100  # 最小区域面积
        filtered_labels = np.zeros_like(labels)
        
        for i in range(1, num_labels):
            mask = (labels == i)
            if np.sum(mask) >= min_area:
                filtered_labels[mask] = i
        
        return filtered_labels
    
    def classify_damage_regions(self, image, segmented_regions):
        """对分割出的区域进行损伤类型分类"""
        if self.classifier is None:
            raise ValueError("分类器未训练，请先调用train_classifier方法")
        
        region_info = []
        unique_labels = np.unique(segmented_regions)
        
        for label in unique_labels:
            if label == 0:  # 跳过背景
                continue
            
            # 获取区域掩码
            mask = (segmented_regions == label)
            
            # 获取边界框
            coords = np.where(mask)
            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()
            
            # 提取区域
            region = image[y_min:y_max+1, x_min:x_max+1]
            
            if region.size > 0:
                # 提取特征并分类
                features = self.extract_features(region)
                features_scaled = self.scaler.transform([features])
                
                # 预测类别和概率
                pred_class = self.classifier.predict(features_scaled)[0]
                pred_proba = self.classifier.predict_proba(features_scaled)[0]
                
                # 计算区域面积（像素）
                area_pixels = np.sum(mask)
                
                region_info.append({
                    'label': label,
                    'class': self.class_names[pred_class],
                    'confidence': pred_proba[pred_class],
                    'area_pixels': area_pixels,
                    'bbox': (x_min, y_min, x_max, y_max),
                    'mask': mask
                })
        
        return region_info
    
    def calculate_real_areas(self, region_info, scale_pixels, scale_real_value, scale_unit):
        """计算实际面积"""
        if scale_pixels is None:
            print("警告: 未检测到比例尺，无法计算实际面积")
            return region_info
        
        # 计算像素到实际尺寸的转换比例
        pixel_to_real = scale_real_value / scale_pixels
        
        for region in region_info:
            # 计算实际面积
            real_area = region['area_pixels'] * (pixel_to_real ** 2)
            region['area_real'] = real_area
            region['area_unit'] = scale_unit + '²'
        
        return region_info
    
    def visualize_results(self, image, region_info, output_path=None):
        """可视化检测结果"""
        result_image = image.copy()
        
        # 创建图例
        legend_info = {}
        
        for region in region_info:
            class_name = region['class']
            color = self.colors.get(class_name, (128, 128, 128))
            
            # 在原图上绘制轮廓
            mask = region['mask'].astype(np.uint8) * 255
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            cv2.drawContours(result_image, contours, -1, color, 2)
            
            # 添加标签
            x_min, y_min, x_max, y_max = region['bbox']
            label_text = f"{class_name}"
            if 'area_real' in region:
                label_text += f"\n{region['area_real']:.2f}{region['area_unit']}"
            
            cv2.putText(result_image, class_name, (x_min, y_min-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # 统计信息
            if class_name not in legend_info:
                legend_info[class_name] = {'count': 0, 'total_area': 0}
            legend_info[class_name]['count'] += 1
            if 'area_real' in region:
                legend_info[class_name]['total_area'] += region['area_real']
        
        # 显示结果
        plt.figure(figsize=(15, 10))
        plt.imshow(result_image)
        plt.title('损伤检测和分类结果')
        plt.axis('off')
        
        # 添加图例
        legend_text = "检测结果:\n"
        for class_name, info in legend_info.items():
            color_rgb = [c/255.0 for c in self.colors.get(class_name, (128, 128, 128))]
            legend_text += f"{class_name}: {info['count']}个"
            if info['total_area'] > 0:
                legend_text += f", 总面积: {info['total_area']:.2f}μm²"
            legend_text += "\n"
        
        plt.text(0.02, 0.98, legend_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return result_image, legend_info
    
    def process_image(self, image_path, output_path=None):
        """处理单张图像的完整流程"""
        print(f"处理图像: {image_path}")
        
        # 加载图像
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 1. 检测比例尺
        scale_pixels, scale_text = self.detect_scale_bar(image)
        print(f"检测到比例尺: {scale_pixels} 像素, 文字: {scale_text}")
        
        # 解析比例尺数值（简化版）
        scale_value = 100  # 默认100微米
        scale_unit = "μm"
        
        # 2. 分割损伤区域
        segmented = self.segment_damage_regions(image)
        print(f"分割出 {len(np.unique(segmented))-1} 个区域")
        
        # 3. 分类损伤类型
        region_info = self.classify_damage_regions(image, segmented)
        print(f"分类完成，识别出 {len(region_info)} 个损伤区域")
        
        # 4. 计算实际面积
        region_info = self.calculate_real_areas(region_info, scale_pixels, scale_value, scale_unit)
        
        # 5. 可视化结果
        result_image, summary = self.visualize_results(image, region_info, output_path)
        
        return region_info, summary
    
    def save_model(self, model_path):
        """保存训练好的模型"""
        model_data = {
            'classifier': self.classifier,
            'scaler': self.scaler,
            'class_names': self.class_names
        }
        joblib.dump(model_data, model_path)
        print(f"模型已保存到: {model_path}")
    
    def load_model(self, model_path):
        """加载训练好的模型"""
        model_data = joblib.load(model_path)
        self.classifier = model_data['classifier']
        self.scaler = model_data['scaler']
        self.class_names = model_data['class_names']
        print(f"模型已从 {model_path} 加载")

def main():
    """主函数演示"""
    system = DamageDetectionSystem()
    
    # 训练分类器
    dataset_path = "不同损伤类型图片"
    if os.path.exists(dataset_path):
        accuracy = system.train_classifier(dataset_path)
        system.save_model("damage_detection_model.pkl")
        
        print(f"\n训练完成，准确率: {accuracy:.4f}")
        
        # 处理测试图像
        test_images = []
        for class_name in system.class_names:
            class_path = os.path.join(dataset_path, class_name)
            if os.path.exists(class_path):
                images = [f for f in os.listdir(class_path) 
                         if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
                if images:
                    test_images.append(os.path.join(class_path, images[0]))
        
        # 处理第一张测试图像
        if test_images:
            print(f"\n处理测试图像: {test_images[0]}")
            region_info, summary = system.process_image(test_images[0], "damage_analysis_result.png")
            
            print("\n检测结果:")
            for class_name, info in summary.items():
                print(f"{class_name}: {info['count']}个区域, 总面积: {info['total_area']:.2f}μm²")
    
    else:
        print(f"数据集路径不存在: {dataset_path}")

if __name__ == "__main__":
    main()
