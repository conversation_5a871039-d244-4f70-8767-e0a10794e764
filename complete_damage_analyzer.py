import os
import numpy as np
import cv2
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image, ImageDraw, ImageFont
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import joblib
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CompleteDamageAnalyzer:
    def __init__(self):
        self.classifier = None
        self.scaler = None
        self.class_names = ['剥落', '压伤', '擦伤', '点蚀', '磨损']
        self.colors = {
            '剥落': (255, 0, 0),      # 红色 - Spalling
            '压伤': (0, 255, 0),      # 绿色 - Compression damage  
            '擦伤': (0, 0, 255),      # 蓝色 - Scratching
            '点蚀': (255, 255, 0),    # 黄色 - Pitting
            '磨损': (255, 0, 255)     # 紫色 - Wear
        }
    
    def extract_comprehensive_features(self, image_patch):
        """提取综合特征"""
        # 确保输入是RGB格式
        if len(image_patch.shape) == 3:
            patch = cv2.resize(image_patch, (64, 64))
            gray = cv2.cvtColor(patch, cv2.COLOR_RGB2GRAY)
        else:
            gray = cv2.resize(image_patch, (64, 64))
            patch = cv2.cvtColor(gray, cv2.COLOR_GRAY2RGB)
        
        features = []
        
        # 1. 灰度统计特征
        features.extend([
            np.mean(gray), np.std(gray), np.median(gray),
            np.percentile(gray, 25), np.percentile(gray, 75),
            np.min(gray), np.max(gray), np.var(gray)
        ])
        
        # 2. 纹理特征 - 简化LBP
        lbp = self._compute_lbp(gray)
        hist, _ = np.histogram(lbp.ravel(), bins=10, range=(0, 10))
        features.extend(hist / (np.sum(hist) + 1e-7))
        
        # 3. 边缘特征
        edges = cv2.Canny(gray, 30, 100)
        edge_density = np.sum(edges > 0) / edges.size
        features.append(edge_density)
        
        # 4. 梯度特征
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        features.extend([
            np.mean(grad_magnitude), np.std(grad_magnitude),
            np.max(grad_magnitude)
        ])
        
        # 5. 颜色特征
        for channel in range(3):
            channel_data = patch[:, :, channel]
            features.extend([
                np.mean(channel_data), np.std(channel_data),
                np.median(channel_data)
            ])
        
        # 6. 形状特征
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            perimeter = cv2.arcLength(largest_contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
            else:
                circularity = 0
            features.extend([area / (64*64), perimeter / (64*4), circularity])
        else:
            features.extend([0, 0, 0])
        
        # 7. 频域特征
        fft = np.fft.fft2(gray)
        fft_magnitude = np.abs(fft)
        features.extend([
            np.mean(fft_magnitude), np.std(fft_magnitude)
        ])
        
        return np.array(features)
    
    def _compute_lbp(self, image):
        """计算局部二进制模式"""
        rows, cols = image.shape
        lbp = np.zeros_like(image)
        
        for i in range(1, rows-1):
            for j in range(1, cols-1):
                center = image[i, j]
                code = 0
                # 8邻域
                neighbors = [
                    image[i-1, j-1], image[i-1, j], image[i-1, j+1],
                    image[i, j+1], image[i+1, j+1], image[i+1, j],
                    image[i+1, j-1], image[i, j-1]
                ]
                
                for k, neighbor in enumerate(neighbors):
                    if neighbor >= center:
                        code |= (1 << k)
                
                lbp[i, j] = code
        
        return lbp
    
    def train_classifier(self, dataset_path, samples_per_image=20):
        """训练分类器"""
        print("开始训练损伤类型分类器...")
        
        X, y = [], []
        
        for class_idx, class_name in enumerate(self.class_names):
            class_path = os.path.join(dataset_path, class_name)
            if not os.path.exists(class_path):
                print(f"警告: 类别路径不存在 {class_path}")
                continue
                
            images = [f for f in os.listdir(class_path) 
                     if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
            
            print(f"处理类别 '{class_name}': {len(images)} 张图片")
            
            class_samples = 0
            for img_file in images:
                img_path = os.path.join(class_path, img_file)
                try:
                    # 加载图像
                    pil_img = Image.open(img_path)
                    image = np.array(pil_img.convert('RGB'))
                    
                    h, w = image.shape[:2]
                    
                    # 从每张图片中提取多个随机块
                    for _ in range(samples_per_image):
                        # 随机选择64x64的块
                        if h > 64 and w > 64:
                            x = np.random.randint(0, w-64)
                            y = np.random.randint(0, h-64)
                            patch = image[y:y+64, x:x+64]
                        else:
                            patch = cv2.resize(image, (64, 64))
                        
                        # 提取特征
                        features = self.extract_comprehensive_features(patch)
                        X.append(features)
                        y.append(class_idx)
                        class_samples += 1
                        
                except Exception as e:
                    print(f"处理图片 {img_file} 时出错: {e}")
            
            print(f"类别 '{class_name}' 生成了 {class_samples} 个训练样本")
        
        if len(X) == 0:
            print("错误: 没有成功提取任何特征")
            return 0.0
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"总训练样本: {len(X)}, 特征维度: {X.shape[1]}")
        
        # 分割训练和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 特征标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 训练随机森林分类器
        self.classifier = RandomForestClassifier(
            n_estimators=300,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'
        )
        
        print("训练随机森林分类器...")
        self.classifier.fit(X_train_scaled, y_train)
        
        # 评估模型
        y_pred = self.classifier.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"分类器训练完成!")
        print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 详细分类报告
        print("\n分类报告:")
        print(classification_report(y_test, y_pred, target_names=self.class_names))
        
        return accuracy
    
    def detect_scale_bar(self, image):
        """检测比例尺 - 简化版本"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        h, w = gray.shape
        
        # 在图像底部寻找比例尺
        bottom_region = gray[int(h*0.8):, :]
        
        # 检测水平线段
        edges = cv2.Canny(bottom_region, 50, 150)
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=30, 
                               minLineLength=20, maxLineGap=5)
        
        scale_length_pixels = None
        
        if lines is not None:
            # 找到最长的近似水平线
            max_length = 0
            for line in lines:
                x1, y1, x2, y2 = line[0]
                length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                angle = np.abs(np.arctan2(y2-y1, x2-x1) * 180 / np.pi)
                
                # 检查是否为水平线（角度小于15度）
                if angle < 15 or angle > 165:
                    if length > max_length:
                        max_length = length
                        scale_length_pixels = length
        
        # 默认比例尺信息（实际应用中应该用OCR识别）
        scale_real_value = 100  # 100微米
        scale_unit = "μm"
        
        return scale_length_pixels, scale_real_value, scale_unit
    
    def segment_damage_regions(self, image):
        """分割损伤区域"""
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # 1. 预处理
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 2. 多种阈值方法组合
        # Otsu阈值
        _, thresh1 = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 自适应阈值
        thresh2 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY_INV, 11, 2)
        
        # 边缘检测
        edges = cv2.Canny(blurred, 30, 100)
        
        # 组合结果
        combined = cv2.bitwise_or(thresh1, thresh2)
        combined = cv2.bitwise_or(combined, edges)
        
        # 3. 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
        combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel)
        
        # 4. 连通组件分析
        num_labels, labels = cv2.connectedComponents(combined)
        
        # 过滤小区域
        min_area = 50  # 最小区域面积
        filtered_labels = np.zeros_like(labels)
        
        for i in range(1, num_labels):
            mask = (labels == i)
            if np.sum(mask) >= min_area:
                filtered_labels[mask] = i
        
        return filtered_labels
    
    def classify_regions(self, image, segmented_regions):
        """对分割区域进行分类"""
        if self.classifier is None:
            raise ValueError("分类器未训练，请先调用train_classifier方法")
        
        region_info = []
        unique_labels = np.unique(segmented_regions)
        
        for label in unique_labels:
            if label == 0:  # 跳过背景
                continue
            
            # 获取区域掩码和边界框
            mask = (segmented_regions == label)
            coords = np.where(mask)
            y_min, y_max = coords[0].min(), coords[0].max()
            x_min, x_max = coords[1].min(), coords[1].max()
            
            # 提取区域图像
            region_img = image[y_min:y_max+1, x_min:x_max+1]
            
            if region_img.size > 0:
                # 调整大小并提取特征
                region_resized = cv2.resize(region_img, (64, 64))
                features = self.extract_comprehensive_features(region_resized)
                features_scaled = self.scaler.transform([features])
                
                # 预测类别
                pred_class = self.classifier.predict(features_scaled)[0]
                pred_proba = self.classifier.predict_proba(features_scaled)[0]
                confidence = pred_proba[pred_class]
                
                # 计算区域面积
                area_pixels = np.sum(mask)
                
                region_info.append({
                    'label': label,
                    'class': self.class_names[pred_class],
                    'confidence': confidence,
                    'area_pixels': area_pixels,
                    'bbox': (x_min, y_min, x_max, y_max),
                    'mask': mask
                })
        
        return region_info
    
    def calculate_real_areas(self, region_info, scale_pixels, scale_real, scale_unit):
        """计算实际面积"""
        if scale_pixels is None or scale_pixels == 0:
            print("警告: 未检测到有效比例尺，使用默认值")
            scale_pixels = 100  # 默认值
        
        # 计算像素到实际尺寸的转换比例
        pixel_to_real = scale_real / scale_pixels
        
        for region in region_info:
            real_area = region['area_pixels'] * (pixel_to_real ** 2)
            region['area_real'] = real_area
            region['area_unit'] = scale_unit + '²'
        
        return region_info
    
    def visualize_results(self, image, region_info, output_path=None):
        """可视化检测结果"""
        result_image = image.copy()
        
        # 统计信息
        damage_stats = {}
        
        # 绘制检测结果
        for i, region in enumerate(region_info):
            class_name = region['class']
            color = self.colors.get(class_name, (128, 128, 128))
            confidence = region['confidence']
            
            # 只显示置信度较高的结果
            if confidence < 0.3:
                continue
            
            # 绘制轮廓
            mask = region['mask'].astype(np.uint8) * 255
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            cv2.drawContours(result_image, contours, -1, color, 2)
            
            # 添加标签
            x_min, y_min, x_max, y_max = region['bbox']
            label_text = f"{class_name}"
            cv2.putText(result_image, label_text, (x_min, y_min-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 统计
            if class_name not in damage_stats:
                damage_stats[class_name] = {'count': 0, 'total_area': 0}
            damage_stats[class_name]['count'] += 1
            if 'area_real' in region:
                damage_stats[class_name]['total_area'] += region['area_real']
        
        # 显示结果
        plt.figure(figsize=(15, 10))
        plt.imshow(result_image)
        plt.title('损伤检测和分类结果', fontsize=16)
        plt.axis('off')
        
        # 创建图例
        legend_text = "检测结果:\n"
        for class_name, stats in damage_stats.items():
            legend_text += f"{class_name}: {stats['count']}个"
            if stats['total_area'] > 0:
                legend_text += f", {stats['total_area']:.1f}μm²"
            legend_text += "\n"
        
        plt.text(0.02, 0.98, legend_text, transform=plt.gca().transAxes, 
                verticalalignment='top', fontsize=12,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return result_image, damage_stats
    
    def analyze_image(self, image_path, output_path=None):
        """分析单张图像的完整流程"""
        print(f"分析图像: {os.path.basename(image_path)}")
        
        # 1. 加载图像
        pil_img = Image.open(image_path)
        image = np.array(pil_img.convert('RGB'))
        print(f"图像尺寸: {image.shape}")
        
        # 2. 检测比例尺
        scale_pixels, scale_real, scale_unit = self.detect_scale_bar(image)
        print(f"比例尺: {scale_pixels} 像素 = {scale_real} {scale_unit}")
        
        # 3. 分割损伤区域
        segmented = self.segment_damage_regions(image)
        num_regions = len(np.unique(segmented)) - 1
        print(f"检测到 {num_regions} 个潜在损伤区域")
        
        # 4. 分类损伤类型
        region_info = self.classify_regions(image, segmented)
        print(f"分类完成，识别出 {len(region_info)} 个损伤区域")
        
        # 5. 计算实际面积
        region_info = self.calculate_real_areas(region_info, scale_pixels, scale_real, scale_unit)
        
        # 6. 可视化结果
        result_image, stats = self.visualize_results(image, region_info, output_path)
        
        # 7. 输出详细结果
        print("\n=== 损伤分析结果 ===")
        for class_name, info in stats.items():
            print(f"{class_name}: {info['count']} 个区域, 总面积: {info['total_area']:.2f} μm²")
        
        return region_info, stats
    
    def save_model(self, model_path):
        """保存模型"""
        model_data = {
            'classifier': self.classifier,
            'scaler': self.scaler,
            'class_names': self.class_names
        }
        joblib.dump(model_data, model_path)
        print(f"模型已保存: {model_path}")
    
    def load_model(self, model_path):
        """加载模型"""
        model_data = joblib.load(model_path)
        self.classifier = model_data['classifier']
        self.scaler = model_data['scaler']
        self.class_names = model_data['class_names']
        print(f"模型已加载: {model_path}")

def main():
    """主函数"""
    print("="*60)
    print("完整损伤检测分析系统")
    print("="*60)
    
    analyzer = CompleteDamageAnalyzer()
    
    # 训练分类器
    dataset_path = "不同损伤类型图片"
    if os.path.exists(dataset_path):
        print("开始训练分类器...")
        accuracy = analyzer.train_classifier(dataset_path, samples_per_image=30)
        
        if accuracy > 0:
            # 保存模型
            analyzer.save_model("complete_damage_model.pkl")
            
            # 测试分析
            print(f"\n训练完成，准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
            
            # 选择一张测试图像
            test_image = None
            for class_name in analyzer.class_names:
                class_path = os.path.join(dataset_path, class_name)
                if os.path.exists(class_path):
                    images = [f for f in os.listdir(class_path) 
                             if f.lower().endswith(('.tif', '.tiff'))]
                    if images:
                        test_image = os.path.join(class_path, images[0])
                        break
            
            if test_image:
                print(f"\n分析测试图像: {test_image}")
                region_info, stats = analyzer.analyze_image(test_image, "damage_analysis_result.png")
        else:
            print("训练失败")
    else:
        print(f"数据集路径不存在: {dataset_path}")

if __name__ == "__main__":
    main()
