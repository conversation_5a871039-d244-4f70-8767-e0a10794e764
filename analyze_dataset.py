import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import pandas as pd
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

def analyze_dataset(dataset_path):
    """分析损伤类型图像数据集"""
    
    # 获取所有类别
    categories = [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
    print(f"发现 {len(categories)} 个损伤类别: {categories}")
    
    # 统计信息
    stats = defaultdict(list)
    image_info = []
    
    for category in categories:
        category_path = os.path.join(dataset_path, category)
        images = [f for f in os.listdir(category_path) if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"\n分析类别: {category}")
        print(f"图片数量: {len(images)}")
        
        category_stats = {
            'widths': [],
            'heights': [],
            'file_sizes': [],
            'modes': []
        }
        
        for img_file in images:
            img_path = os.path.join(category_path, img_file)
            try:
                # 获取文件大小
                file_size = os.path.getsize(img_path) / (1024 * 1024)  # MB
                
                # 打开图像获取信息
                with Image.open(img_path) as img:
                    width, height = img.size
                    mode = img.mode
                    
                    category_stats['widths'].append(width)
                    category_stats['heights'].append(height)
                    category_stats['file_sizes'].append(file_size)
                    category_stats['modes'].append(mode)
                    
                    image_info.append({
                        'category': category,
                        'filename': img_file,
                        'width': width,
                        'height': height,
                        'file_size_mb': file_size,
                        'mode': mode
                    })
                    
            except Exception as e:
                print(f"处理图片 {img_file} 时出错: {e}")
        
        # 计算统计信息
        if category_stats['widths']:
            stats[category] = {
                'count': len(images),
                'avg_width': np.mean(category_stats['widths']),
                'avg_height': np.mean(category_stats['heights']),
                'avg_file_size': np.mean(category_stats['file_sizes']),
                'width_std': np.std(category_stats['widths']),
                'height_std': np.std(category_stats['heights']),
                'modes': list(set(category_stats['modes']))
            }
            
            print(f"平均尺寸: {stats[category]['avg_width']:.1f} x {stats[category]['avg_height']:.1f}")
            print(f"平均文件大小: {stats[category]['avg_file_size']:.2f} MB")
            print(f"图像模式: {stats[category]['modes']}")
    
    return stats, image_info, categories

def create_visualizations(stats, image_info, categories):
    """创建数据可视化"""
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('损伤类型数据集分析', fontsize=16, fontweight='bold')
    
    # 1. 类别分布
    counts = [stats[cat]['count'] for cat in categories]
    axes[0, 0].bar(categories, counts, color='skyblue', edgecolor='navy', alpha=0.7)
    axes[0, 0].set_title('各类别样本数量分布')
    axes[0, 0].set_ylabel('样本数量')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for i, v in enumerate(counts):
        axes[0, 0].text(i, v + 0.5, str(v), ha='center', va='bottom')
    
    # 2. 图像尺寸分布
    df = pd.DataFrame(image_info)
    for i, cat in enumerate(categories):
        cat_data = df[df['category'] == cat]
        axes[0, 1].scatter(cat_data['width'], cat_data['height'], 
                          label=cat, alpha=0.6, s=30)
    axes[0, 1].set_title('图像尺寸分布')
    axes[0, 1].set_xlabel('宽度 (像素)')
    axes[0, 1].set_ylabel('高度 (像素)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 文件大小分布
    axes[0, 2].boxplot([df[df['category'] == cat]['file_size_mb'].values for cat in categories],
                       labels=categories)
    axes[0, 2].set_title('文件大小分布')
    axes[0, 2].set_ylabel('文件大小 (MB)')
    axes[0, 2].tick_params(axis='x', rotation=45)
    
    # 4. 平均尺寸对比
    avg_widths = [stats[cat]['avg_width'] for cat in categories]
    avg_heights = [stats[cat]['avg_height'] for cat in categories]
    
    x = np.arange(len(categories))
    width = 0.35
    
    axes[1, 0].bar(x - width/2, avg_widths, width, label='平均宽度', alpha=0.7)
    axes[1, 0].bar(x + width/2, avg_heights, width, label='平均高度', alpha=0.7)
    axes[1, 0].set_title('各类别平均图像尺寸')
    axes[1, 0].set_ylabel('像素')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(categories, rotation=45)
    axes[1, 0].legend()
    
    # 5. 类别不平衡可视化
    total_samples = sum(counts)
    percentages = [count/total_samples*100 for count in counts]
    
    colors = plt.cm.Set3(np.linspace(0, 1, len(categories)))
    wedges, texts, autotexts = axes[1, 1].pie(percentages, labels=categories, autopct='%1.1f%%',
                                             colors=colors, startangle=90)
    axes[1, 1].set_title('类别分布比例')
    
    # 6. 数据质量评估
    quality_metrics = []
    for cat in categories:
        cat_data = df[df['category'] == cat]
        # 计算尺寸一致性（标准差越小越好）
        size_consistency = 1 / (1 + stats[cat]['width_std'] + stats[cat]['height_std'])
        # 样本充足性（相对于最大类别）
        sample_adequacy = stats[cat]['count'] / max(counts)
        
        quality_metrics.append({
            'category': cat,
            'size_consistency': size_consistency,
            'sample_adequacy': sample_adequacy,
            'overall_quality': (size_consistency + sample_adequacy) / 2
        })
    
    quality_df = pd.DataFrame(quality_metrics)
    
    axes[1, 2].barh(quality_df['category'], quality_df['overall_quality'], color='lightgreen', alpha=0.7)
    axes[1, 2].set_title('数据质量评分')
    axes[1, 2].set_xlabel('质量评分 (0-1)')
    axes[1, 2].set_xlim(0, 1)
    
    plt.tight_layout()
    plt.savefig('dataset_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return quality_df

def print_summary(stats, categories, quality_df):
    """打印数据集摘要"""
    print("\n" + "="*60)
    print("数据集分析摘要")
    print("="*60)
    
    total_images = sum(stats[cat]['count'] for cat in categories)
    print(f"总图片数量: {total_images}")
    print(f"类别数量: {len(categories)}")
    
    # 类别不平衡分析
    counts = [stats[cat]['count'] for cat in categories]
    max_count = max(counts)
    min_count = min(counts)
    imbalance_ratio = max_count / min_count
    
    print(f"\n类别不平衡分析:")
    print(f"最大类别样本数: {max_count}")
    print(f"最小类别样本数: {min_count}")
    print(f"不平衡比例: {imbalance_ratio:.2f}:1")
    
    if imbalance_ratio > 2:
        print("⚠️  检测到严重的类别不平衡，建议进行数据增强")
    
    # 图像尺寸分析
    all_widths = []
    all_heights = []
    for cat in categories:
        all_widths.extend([stats[cat]['avg_width']] * stats[cat]['count'])
        all_heights.extend([stats[cat]['avg_height']] * stats[cat]['count'])
    
    print(f"\n图像尺寸分析:")
    print(f"平均宽度: {np.mean(all_widths):.1f} ± {np.std(all_widths):.1f}")
    print(f"平均高度: {np.mean(all_heights):.1f} ± {np.std(all_heights):.1f}")
    
    # 数据质量评估
    avg_quality = quality_df['overall_quality'].mean()
    print(f"\n数据质量评估:")
    print(f"平均质量评分: {avg_quality:.3f}")
    
    if avg_quality > 0.7:
        print("✅ 数据质量良好")
    elif avg_quality > 0.5:
        print("⚠️  数据质量中等，建议优化")
    else:
        print("❌ 数据质量较差，需要大量预处理")
    
    # 建议
    print(f"\n建议:")
    print("1. 使用数据增强技术平衡类别分布")
    print("2. 标准化图像尺寸到统一大小")
    print("3. 使用预训练模型进行迁移学习")
    print("4. 采用交叉验证确保模型泛化能力")
    
    if total_images < 100:
        print("5. ⚠️  数据集较小，建议收集更多数据或使用强数据增强")

if __name__ == "__main__":
    dataset_path = "不同损伤类型图片"
    
    if not os.path.exists(dataset_path):
        print(f"错误: 找不到数据集路径 {dataset_path}")
        exit(1)
    
    print("开始分析损伤类型数据集...")
    stats, image_info, categories = analyze_dataset(dataset_path)
    
    print("\n创建可视化图表...")
    quality_df = create_visualizations(stats, image_info, categories)
    
    print_summary(stats, categories, quality_df)
    
    print(f"\n分析完成！图表已保存为 'dataset_analysis.png'")
