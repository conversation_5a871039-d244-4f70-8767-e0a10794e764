import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import cv2
from PIL import Image
from skimage import feature, measure, filters, segmentation
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)

def extract_texture_features(image):
    """提取纹理特征"""
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    features = []
    
    # LBP特征
    lbp = feature.local_binary_pattern(gray, P=8, R=1, method='uniform')
    lbp_hist, _ = np.histogram(lbp.ravel(), bins=10, range=(0, 10))
    features.extend(lbp_hist / np.sum(lbp_hist))
    
    # GLCM特征
    glcm = feature.graycomatrix(gray, [1], [0, np.pi/4, np.pi/2, 3*np.pi/4], levels=256, symmetric=True, normed=True)
    contrast = feature.graycoprops(glcm, 'contrast').flatten()
    dissimilarity = feature.graycoprops(glcm, 'dissimilarity').flatten()
    homogeneity = feature.graycoprops(glcm, 'homogeneity').flatten()
    energy = feature.graycoprops(glcm, 'energy').flatten()
    
    features.extend(contrast)
    features.extend(dissimilarity)
    features.extend(homogeneity)
    features.extend(energy)
    
    return np.array(features)

def extract_shape_features(image):
    """提取形状特征"""
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    # 边缘检测
    edges = feature.canny(gray, sigma=1)
    
    # 区域属性
    labeled = measure.label(edges)
    regions = measure.regionprops(labeled)
    
    features = []
    
    if regions:
        # 取最大区域的特征
        largest_region = max(regions, key=lambda r: r.area)
        features.extend([
            largest_region.area,
            largest_region.perimeter,
            largest_region.eccentricity,
            largest_region.solidity,
            largest_region.extent
        ])
    else:
        features.extend([0, 0, 0, 0, 0])
    
    # 边缘密度
    edge_density = np.sum(edges) / edges.size
    features.append(edge_density)
    
    return np.array(features)

def extract_color_features(image):
    """提取颜色特征"""
    features = []
    
    # RGB统计特征
    for channel in range(3):
        channel_data = image[:, :, channel]
        features.extend([
            np.mean(channel_data),
            np.std(channel_data),
            np.median(channel_data),
            np.percentile(channel_data, 25),
            np.percentile(channel_data, 75)
        ])
    
    # HSV特征
    hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
    for channel in range(3):
        channel_data = hsv[:, :, channel]
        features.extend([
            np.mean(channel_data),
            np.std(channel_data)
        ])
    
    # 颜色直方图
    hist_r = cv2.calcHist([image], [0], None, [8], [0, 256])
    hist_g = cv2.calcHist([image], [1], None, [8], [0, 256])
    hist_b = cv2.calcHist([image], [2], None, [8], [0, 256])
    
    features.extend(hist_r.flatten() / np.sum(hist_r))
    features.extend(hist_g.flatten() / np.sum(hist_g))
    features.extend(hist_b.flatten() / np.sum(hist_b))
    
    return np.array(features)

def extract_frequency_features(image):
    """提取频域特征"""
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    # FFT
    f_transform = np.fft.fft2(gray)
    f_shift = np.fft.fftshift(f_transform)
    magnitude_spectrum = np.log(np.abs(f_shift) + 1)
    
    # 频域统计特征
    features = [
        np.mean(magnitude_spectrum),
        np.std(magnitude_spectrum),
        np.median(magnitude_spectrum)
    ]
    
    # Gabor滤波器响应
    gabor_responses = []
    for theta in [0, 45, 90, 135]:
        real, _ = filters.gabor(gray, frequency=0.1, theta=np.deg2rad(theta))
        gabor_responses.extend([np.mean(real), np.std(real)])
    
    features.extend(gabor_responses)
    
    return np.array(features)

def extract_all_features(image):
    """提取所有特征"""
    # 调整图像大小以保持一致性
    image_resized = cv2.resize(image, (256, 256))
    
    texture_feat = extract_texture_features(image_resized)
    shape_feat = extract_shape_features(image_resized)
    color_feat = extract_color_features(image_resized)
    freq_feat = extract_frequency_features(image_resized)
    
    all_features = np.concatenate([texture_feat, shape_feat, color_feat, freq_feat])
    
    return all_features

def load_data_with_features(dataset_path):
    """加载数据并提取特征"""
    print("正在加载数据并提取特征...")
    
    class_names = sorted([d for d in os.listdir(dataset_path) 
                         if os.path.isdir(os.path.join(dataset_path, d))])
    print(f"发现类别: {class_names}")
    
    X, y = [], []
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_path, class_name)
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"处理类别 '{class_name}': {len(images)} 张图片")
        
        for img_file in images:
            img_path = os.path.join(class_path, img_file)
            try:
                # 加载图像
                pil_img = Image.open(img_path)
                img = np.array(pil_img.convert('RGB'))
                
                # 提取特征
                features = extract_all_features(img)
                
                X.append(features)
                y.append(class_idx)
                
            except Exception as e:
                print(f"处理图片 {img_file} 时出错: {e}")
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"特征提取完成: {len(X)} 个样本, {X.shape[1]} 个特征")
    
    # 打印类别分布
    unique, counts = np.unique(y, return_counts=True)
    for i, (class_idx, count) in enumerate(zip(unique, counts)):
        print(f"{class_names[class_idx]}: {count} 张")
    
    return X, y, class_names

def train_ensemble_model(X, y, class_names):
    """训练集成模型"""
    print("开始训练集成模型...")
    
    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {len(X_train)} 个样本")
    print(f"测试集: {len(X_test)} 个样本")
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 降维（可选）
    pca = PCA(n_components=0.95)  # 保留95%的方差
    X_train_pca = pca.fit_transform(X_train_scaled)
    X_test_pca = pca.transform(X_test_scaled)
    
    print(f"PCA后特征数: {X_train_pca.shape[1]}")
    
    # 定义基础分类器
    rf = RandomForestClassifier(
        n_estimators=200,
        max_depth=10,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42,
        class_weight='balanced'
    )
    
    gb = GradientBoostingClassifier(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
    
    svm = SVC(
        kernel='rbf',
        C=10,
        gamma='scale',
        probability=True,
        random_state=42,
        class_weight='balanced'
    )
    
    # 创建集成分类器
    ensemble = VotingClassifier(
        estimators=[
            ('rf', rf),
            ('gb', gb),
            ('svm', svm)
        ],
        voting='soft'
    )
    
    # 训练模型
    print("训练集成模型...")
    ensemble.fit(X_train_pca, y_train)
    
    # 交叉验证评估
    cv_scores = cross_val_score(ensemble, X_train_pca, y_train, cv=5, scoring='accuracy')
    print(f"交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    # 测试集评估
    y_pred = ensemble.predict(X_test_pca)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 分类报告
    print("\n分类报告:")
    print(classification_report(y_test, y_pred, target_names=class_names))
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    
    # 绘制混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    annotations = []
    for i in range(cm.shape[0]):
        row = []
        for j in range(cm.shape[1]):
            row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
        annotations.append(row)
    
    sns.heatmap(
        cm_percent, 
        annot=annotations, 
        fmt='',
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={'label': '百分比 (%)'}
    )
    
    plt.title('特征工程+集成学习 - 混淆矩阵')
    plt.xlabel('预测类别')
    plt.ylabel('真实类别')
    plt.tight_layout()
    plt.savefig('feature_based_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return ensemble, scaler, pca, accuracy

def main():
    """主函数"""
    print("="*60)
    print("基于特征工程的损伤类型分类器 - 目标准确率95%+")
    print("="*60)
    
    # 加载数据并提取特征
    X, y, class_names = load_data_with_features("不同损伤类型图片")
    
    # 训练集成模型
    model, scaler, pca, accuracy = train_ensemble_model(X, y, class_names)
    
    print(f"\n最终测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    if accuracy >= 0.95:
        print("🎉 恭喜！模型达到了95%以上的准确率目标！")
    else:
        print(f"⚠️ 模型准确率为 {accuracy*100:.2f}%，未达到95%目标")
        if accuracy >= 0.80:
            print("✅ 但已达到80%以上，这对于小数据集来说是很好的结果！")
    
    # 保存模型组件
    import joblib
    joblib.dump(model, 'feature_based_ensemble_model.pkl')
    joblib.dump(scaler, 'feature_scaler.pkl')
    joblib.dump(pca, 'feature_pca.pkl')
    
    print("模型组件已保存")
    
    return model, accuracy

if __name__ == "__main__":
    model, accuracy = main()
