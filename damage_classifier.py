import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from tensorflow.keras.applications import EfficientNetB0, ResNet50V2
from tensorflow.keras.utils import to_categorical
import cv2
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

class DamageClassifier:
    def __init__(self, dataset_path, img_size=(224, 224), batch_size=16):
        self.dataset_path = dataset_path
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        self.class_names = []
        self.X = []
        self.y = []
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        
        # 获取类别名称
        self.class_names = sorted([d for d in os.listdir(self.dataset_path) 
                                 if os.path.isdir(os.path.join(self.dataset_path, d))])
        print(f"发现类别: {self.class_names}")
        
        # 加载所有图像和标签
        for class_idx, class_name in enumerate(self.class_names):
            class_path = os.path.join(self.dataset_path, class_name)
            images = [f for f in os.listdir(class_path) 
                     if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
            
            print(f"加载类别 '{class_name}': {len(images)} 张图片")
            
            for img_file in images:
                img_path = os.path.join(class_path, img_file)
                try:
                    # 尝试多种方式加载图像
                    img = None

                    # 方法1: 使用PIL加载TIFF
                    try:
                        from PIL import Image
                        pil_img = Image.open(img_path)
                        img = np.array(pil_img.convert('RGB'))
                    except:
                        pass

                    # 方法2: 使用OpenCV
                    if img is None:
                        img = cv2.imread(img_path)
                        if img is not None:
                            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

                    # 方法3: 使用OpenCV的IMREAD_UNCHANGED
                    if img is None:
                        img = cv2.imread(img_path, cv2.IMREAD_UNCHANGED)
                        if img is not None and len(img.shape) == 3:
                            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        elif img is not None and len(img.shape) == 2:
                            img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)

                    if img is not None:
                        # 预处理图像
                        img = cv2.resize(img, self.img_size)
                        img = img.astype(np.float32) / 255.0

                        self.X.append(img)
                        self.y.append(class_idx)
                    else:
                        print(f"无法加载图片: {img_file}")

                except Exception as e:
                    print(f"处理图片 {img_file} 时出错: {e}")
        
        self.X = np.array(self.X)
        self.y = np.array(self.y)
        
        print(f"数据加载完成: {len(self.X)} 张图片, {len(self.class_names)} 个类别")
        print(f"图像形状: {self.X.shape}")
        
        # 打印类别分布
        unique, counts = np.unique(self.y, return_counts=True)
        for i, (class_idx, count) in enumerate(zip(unique, counts)):
            print(f"{self.class_names[class_idx]}: {count} 张")
    
    def create_data_generators(self, X_train, X_val, y_train, y_val):
        """创建数据增强生成器"""
        
        # 训练数据增强 - 强数据增强来平衡类别
        train_datagen = ImageDataGenerator(
            rotation_range=30,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        # 验证数据不增强
        val_datagen = ImageDataGenerator()
        
        train_generator = train_datagen.flow(
            X_train, y_train,
            batch_size=self.batch_size,
            shuffle=True
        )
        
        val_generator = val_datagen.flow(
            X_val, y_val,
            batch_size=self.batch_size,
            shuffle=False
        )
        
        return train_generator, val_generator
    
    def build_model(self, model_type='efficientnet'):
        """构建模型"""
        print(f"构建 {model_type} 模型...")
        
        # 输入层
        inputs = layers.Input(shape=(*self.img_size, 3))
        
        # 预训练基础模型
        if model_type == 'efficientnet':
            base_model = EfficientNetB0(
                weights='imagenet',
                include_top=False,
                input_tensor=inputs
            )
        else:  # resnet
            base_model = ResNet50V2(
                weights='imagenet',
                include_top=False,
                input_tensor=inputs
            )
        
        # 冻结预训练层
        base_model.trainable = False
        
        # 添加自定义分类头
        x = base_model.output
        x = layers.GlobalAveragePooling2D()(x)
        x = layers.Dropout(0.3)(x)
        x = layers.Dense(512, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.3)(x)
        outputs = layers.Dense(len(self.class_names), activation='softmax')(x)
        
        self.model = models.Model(inputs, outputs)
        
        # 编译模型
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("模型构建完成!")
        print(f"模型参数总数: {self.model.count_params():,}")
        
        return self.model
    
    def train_model(self, X_train, X_val, y_train, y_val, epochs=50):
        """训练模型"""
        print("开始训练模型...")
        
        # 计算类别权重
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(y_train),
            y=y_train
        )
        class_weight_dict = dict(enumerate(class_weights))
        print(f"类别权重: {class_weight_dict}")
        
        # 创建数据生成器
        train_gen, val_gen = self.create_data_generators(X_train, X_val, y_train, y_val)
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                'best_damage_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 计算每个epoch的步数
        steps_per_epoch = max(1, len(X_train) // self.batch_size)
        validation_steps = max(1, len(X_val) // self.batch_size)
        
        # 训练模型
        self.history = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_gen,
            validation_steps=validation_steps,
            class_weight=class_weight_dict,
            callbacks=callbacks_list,
            verbose=1
        )
        
        print("模型训练完成!")
        return self.history
    
    def fine_tune_model(self, X_train, X_val, y_train, y_val, epochs=20):
        """微调模型"""
        print("开始微调模型...")

        # 找到预训练基础模型
        base_model = None
        for layer in self.model.layers:
            if hasattr(layer, 'layers') and len(layer.layers) > 10:  # 找到预训练模型
                base_model = layer
                break

        if base_model is not None:
            # 解冻部分层进行微调
            base_model.trainable = True

            # 只微调最后几层
            fine_tune_at = max(0, len(base_model.layers) - 20)
            for layer in base_model.layers[:fine_tune_at]:
                layer.trainable = False
        else:
            print("未找到预训练基础模型，跳过微调")
        
        # 重新编译模型，使用更小的学习率
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=0.0001/10),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        # 创建数据生成器
        train_gen, val_gen = self.create_data_generators(X_train, X_val, y_train, y_val)
        
        # 计算类别权重
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(y_train),
            y=y_train
        )
        class_weight_dict = dict(enumerate(class_weights))
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=8,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=3,
                min_lr=1e-8,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                'best_damage_model_finetuned.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 计算每个epoch的步数
        steps_per_epoch = max(1, len(X_train) // self.batch_size)
        validation_steps = max(1, len(X_val) // self.batch_size)
        
        # 微调训练
        fine_tune_history = self.model.fit(
            train_gen,
            steps_per_epoch=steps_per_epoch,
            epochs=epochs,
            validation_data=val_gen,
            validation_steps=validation_steps,
            class_weight=class_weight_dict,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # 合并训练历史
        if self.history is not None:
            for key in self.history.history.keys():
                self.history.history[key].extend(fine_tune_history.history[key])
        else:
            self.history = fine_tune_history
        
        print("模型微调完成!")
        return fine_tune_history

    def evaluate_model(self, X_test, y_test):
        """评估模型性能"""
        print("评估模型性能...")

        # 预测
        y_pred_proba = self.model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)

        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

        # 分类报告
        report = classification_report(
            y_test, y_pred,
            target_names=self.class_names,
            output_dict=True
        )

        print("\n分类报告:")
        print(classification_report(y_test, y_pred, target_names=self.class_names))

        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)

        return accuracy, report, cm, y_pred, y_pred_proba

    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可绘制")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

        # 准确率
        ax1.plot(self.history.history['accuracy'], label='训练准确率')
        ax1.plot(self.history.history['val_accuracy'], label='验证准确率')
        ax1.set_title('模型准确率')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('准确率')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 损失
        ax2.plot(self.history.history['loss'], label='训练损失')
        ax2.plot(self.history.history['val_loss'], label='验证损失')
        ax2.set_title('模型损失')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('损失')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_confusion_matrix(self, cm):
        """绘制混淆矩阵"""
        plt.figure(figsize=(10, 8))

        # 计算百分比
        cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

        # 创建标注
        annotations = []
        for i in range(cm.shape[0]):
            row = []
            for j in range(cm.shape[1]):
                row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
            annotations.append(row)

        sns.heatmap(
            cm_percent,
            annot=annotations,
            fmt='',
            cmap='Blues',
            xticklabels=self.class_names,
            yticklabels=self.class_names,
            cbar_kws={'label': '百分比 (%)'}
        )

        plt.title('混淆矩阵')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_classification_report(self, report):
        """绘制分类报告热图"""
        # 提取指标数据
        metrics = ['precision', 'recall', 'f1-score']
        classes = [cls for cls in report.keys() if cls not in ['accuracy', 'macro avg', 'weighted avg']]

        data = []
        for cls in classes:
            row = [report[cls][metric] for metric in metrics]
            data.append(row)

        data = np.array(data)

        plt.figure(figsize=(8, 6))
        sns.heatmap(
            data,
            annot=True,
            fmt='.3f',
            cmap='RdYlGn',
            xticklabels=metrics,
            yticklabels=classes,
            vmin=0,
            vmax=1,
            cbar_kws={'label': '分数'}
        )

        plt.title('各类别性能指标')
        plt.xlabel('指标')
        plt.ylabel('类别')
        plt.tight_layout()
        plt.savefig('classification_metrics.png', dpi=300, bbox_inches='tight')
        plt.show()

    def predict_single_image(self, image_path):
        """预测单张图片"""
        try:
            # 加载和预处理图像
            img = cv2.imread(image_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_resized = cv2.resize(img, self.img_size)
            img_normalized = img_resized.astype(np.float32) / 255.0
            img_batch = np.expand_dims(img_normalized, axis=0)

            # 预测
            predictions = self.model.predict(img_batch, verbose=0)
            predicted_class_idx = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class_idx]
            predicted_class = self.class_names[predicted_class_idx]

            # 显示结果
            plt.figure(figsize=(12, 5))

            # 显示原图
            plt.subplot(1, 2, 1)
            plt.imshow(img)
            plt.title(f'原始图像\n预测: {predicted_class}\n置信度: {confidence:.3f}')
            plt.axis('off')

            # 显示预测概率
            plt.subplot(1, 2, 2)
            plt.bar(self.class_names, predictions[0])
            plt.title('各类别预测概率')
            plt.xlabel('损伤类型')
            plt.ylabel('概率')
            plt.xticks(rotation=45)

            plt.tight_layout()
            plt.show()

            return predicted_class, confidence, predictions[0]

        except Exception as e:
            print(f"预测图片时出错: {e}")
            return None, None, None

    def save_model(self, filepath='damage_classifier_final.h5'):
        """保存模型"""
        if self.model is not None:
            self.model.save(filepath)
            print(f"模型已保存到: {filepath}")
        else:
            print("没有训练好的模型可保存")

    def load_model(self, filepath):
        """加载模型"""
        try:
            self.model = tf.keras.models.load_model(filepath)
            print(f"模型已从 {filepath} 加载")
        except Exception as e:
            print(f"加载模型时出错: {e}")

def main():
    """主函数"""
    print("="*60)
    print("损伤类型图像分类器")
    print("="*60)

    # 初始化分类器
    classifier = DamageClassifier(
        dataset_path="不同损伤类型图片",
        img_size=(224, 224),
        batch_size=16
    )

    # 加载数据
    classifier.load_and_preprocess_data()

    # 分割数据集
    X_train, X_temp, y_train, y_temp = train_test_split(
        classifier.X, classifier.y,
        test_size=0.3,
        random_state=42,
        stratify=classifier.y
    )

    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp,
        test_size=0.5,
        random_state=42,
        stratify=y_temp
    )

    print(f"\n数据集分割:")
    print(f"训练集: {len(X_train)} 张")
    print(f"验证集: {len(X_val)} 张")
    print(f"测试集: {len(X_test)} 张")

    # 构建模型
    classifier.build_model('resnet')

    # 训练模型
    classifier.train_model(X_train, X_val, y_train, y_val, epochs=50)

    # 微调模型
    classifier.fine_tune_model(X_train, X_val, y_train, y_val, epochs=20)

    # 评估模型
    accuracy, report, cm, y_pred, y_pred_proba = classifier.evaluate_model(X_test, y_test)

    # 可视化结果
    classifier.plot_training_history()
    classifier.plot_confusion_matrix(cm)
    classifier.plot_classification_report(report)

    # 保存模型
    classifier.save_model()

    print(f"\n最终测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

    if accuracy >= 0.95:
        print("🎉 恭喜！模型达到了95%以上的准确率目标！")
    else:
        print(f"⚠️ 模型准确率为 {accuracy*100:.2f}%，未达到95%目标")
        print("建议：")
        print("1. 增加更多训练数据")
        print("2. 尝试不同的数据增强策略")
        print("3. 调整模型架构或超参数")
        print("4. 使用更强的预训练模型")

    return classifier

if __name__ == "__main__":
    classifier = main()
