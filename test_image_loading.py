import os
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt

def test_image_loading(dataset_path):
    """测试图像加载"""
    print("测试图像加载...")
    
    class_names = ['剥落', '压伤', '擦伤', '点蚀', '磨损']
    
    for class_name in class_names:
        class_path = os.path.join(dataset_path, class_name)
        if not os.path.exists(class_path):
            print(f"类别路径不存在: {class_path}")
            continue
            
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"\n类别 '{class_name}': 发现 {len(images)} 张图片")
        
        success_count = 0
        for i, img_file in enumerate(images[:3]):  # 只测试前3张
            img_path = os.path.join(class_path, img_file)
            print(f"  测试图片: {img_file}")
            
            # 方法1: PIL
            try:
                pil_img = Image.open(img_path)
                img_array = np.array(pil_img.convert('RGB'))
                print(f"    PIL成功: 形状 {img_array.shape}, 类型 {img_array.dtype}")
                success_count += 1
                
                # 显示第一张成功加载的图片
                if success_count == 1:
                    plt.figure(figsize=(8, 6))
                    plt.imshow(img_array)
                    plt.title(f'{class_name} - {img_file}')
                    plt.axis('off')
                    plt.savefig(f'test_{class_name}_sample.png', dpi=150, bbox_inches='tight')
                    plt.show()
                
                break  # 成功加载一张就够了
                
            except Exception as e:
                print(f"    PIL失败: {e}")
            
            # 方法2: OpenCV
            try:
                cv_img = cv2.imread(img_path, cv2.IMREAD_COLOR)
                if cv_img is not None:
                    cv_img_rgb = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
                    print(f"    OpenCV成功: 形状 {cv_img_rgb.shape}, 类型 {cv_img_rgb.dtype}")
                    success_count += 1
                    break
                else:
                    print(f"    OpenCV失败: 返回None")
            except Exception as e:
                print(f"    OpenCV失败: {e}")
        
        print(f"  成功加载: {success_count}/{min(3, len(images))} 张")

def extract_simple_features_test(image):
    """测试特征提取"""
    # 调整大小
    img_resized = cv2.resize(image, (64, 64))
    gray = cv2.cvtColor(img_resized, cv2.COLOR_RGB2GRAY)
    
    features = []
    
    # 基础统计特征
    features.extend([
        np.mean(gray), np.std(gray), np.median(gray),
        np.min(gray), np.max(gray)
    ])
    
    # 边缘特征
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / edges.size
    features.append(edge_density)
    
    # 颜色特征
    for channel in range(3):
        channel_data = img_resized[:, :, channel]
        features.extend([np.mean(channel_data), np.std(channel_data)])
    
    return np.array(features)

def test_feature_extraction(dataset_path):
    """测试特征提取"""
    print("\n测试特征提取...")
    
    class_names = ['剥落', '压伤', '擦伤', '点蚀', '磨损']
    all_features = []
    all_labels = []
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_path, class_name)
        if not os.path.exists(class_path):
            continue
            
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        class_features = []
        
        for img_file in images[:5]:  # 每类只处理5张图片
            img_path = os.path.join(class_path, img_file)
            
            try:
                # 加载图像
                pil_img = Image.open(img_path)
                img_array = np.array(pil_img.convert('RGB'))
                
                # 提取特征
                features = extract_simple_features_test(img_array)
                class_features.append(features)
                all_features.append(features)
                all_labels.append(class_idx)
                
            except Exception as e:
                print(f"处理 {img_file} 时出错: {e}")
        
        if class_features:
            class_features = np.array(class_features)
            print(f"类别 '{class_name}': {len(class_features)} 个样本, {class_features.shape[1]} 个特征")
            print(f"  特征均值: {np.mean(class_features, axis=0)[:5]}")  # 显示前5个特征的均值
    
    if all_features:
        all_features = np.array(all_features)
        all_labels = np.array(all_labels)
        print(f"\n总计: {len(all_features)} 个样本, {all_features.shape[1]} 个特征")
        
        # 简单的分类测试
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        
        if len(set(all_labels)) > 1 and len(all_features) > 10:
            X_train, X_test, y_train, y_test = train_test_split(
                all_features, all_labels, test_size=0.3, random_state=42
            )
            
            clf = RandomForestClassifier(n_estimators=50, random_state=42)
            clf.fit(X_train, y_train)
            
            y_pred = clf.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            print(f"简单分类测试准确率: {accuracy:.4f}")
            
            # 显示特征重要性
            importance = clf.feature_importances_
            print(f"前5个重要特征: {importance[:5]}")
        else:
            print("样本不足，无法进行分类测试")
    
    return all_features, all_labels

def main():
    """主测试函数"""
    dataset_path = "不同损伤类型图片"
    
    if not os.path.exists(dataset_path):
        print(f"数据集路径不存在: {dataset_path}")
        return
    
    # 测试图像加载
    test_image_loading(dataset_path)
    
    # 测试特征提取
    features, labels = test_feature_extraction(dataset_path)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
