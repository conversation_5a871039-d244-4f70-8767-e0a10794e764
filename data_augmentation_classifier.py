import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import cv2
from PIL import Image, ImageEnhance, ImageFilter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)

def augment_image(image, num_augmentations=10):
    """对单张图像进行多种增强"""
    augmented_images = []
    
    for i in range(num_augmentations):
        img = image.copy()
        
        # 随机旋转
        angle = np.random.uniform(-30, 30)
        img = img.rotate(angle, fillcolor=(255, 255, 255))
        
        # 随机缩放
        scale = np.random.uniform(0.8, 1.2)
        w, h = img.size
        new_w, new_h = int(w * scale), int(h * scale)
        img = img.resize((new_w, new_h))
        
        # 如果缩放后尺寸不同，裁剪或填充到原始尺寸
        if new_w != w or new_h != h:
            if new_w > w or new_h > h:
                # 裁剪
                left = (new_w - w) // 2
                top = (new_h - h) // 2
                img = img.crop((left, top, left + w, top + h))
            else:
                # 填充
                new_img = Image.new('RGB', (w, h), (255, 255, 255))
                left = (w - new_w) // 2
                top = (h - new_h) // 2
                new_img.paste(img, (left, top))
                img = new_img
        
        # 随机亮度调整
        brightness = np.random.uniform(0.7, 1.3)
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(brightness)
        
        # 随机对比度调整
        contrast = np.random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(contrast)
        
        # 随机色彩饱和度调整
        color = np.random.uniform(0.8, 1.2)
        enhancer = ImageEnhance.Color(img)
        img = enhancer.enhance(color)
        
        # 随机添加噪声
        img_array = np.array(img)
        noise = np.random.normal(0, 5, img_array.shape)
        img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
        img = Image.fromarray(img_array)
        
        # 随机模糊
        if np.random.random() < 0.3:
            img = img.filter(ImageFilter.GaussianBlur(radius=np.random.uniform(0.5, 1.5)))
        
        # 随机翻转
        if np.random.random() < 0.5:
            img = img.transpose(Image.FLIP_LEFT_RIGHT)
        if np.random.random() < 0.5:
            img = img.transpose(Image.FLIP_TOP_BOTTOM)
        
        augmented_images.append(img)
    
    return augmented_images

def extract_simple_features(image):
    """提取简单但有效的特征"""
    # 转换为numpy数组
    img_array = np.array(image)
    
    # 调整大小
    img_resized = cv2.resize(img_array, (64, 64))
    
    # 转换为灰度图
    gray = cv2.cvtColor(img_resized, cv2.COLOR_RGB2GRAY)
    
    features = []
    
    # 1. 像素统计特征
    features.extend([
        np.mean(gray),
        np.std(gray),
        np.median(gray),
        np.min(gray),
        np.max(gray),
        np.percentile(gray, 25),
        np.percentile(gray, 75)
    ])
    
    # 2. 边缘特征
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / edges.size
    features.append(edge_density)
    
    # 3. 纹理特征（简化版）
    # 计算灰度共生矩阵的简化版本
    glcm_features = []
    for dx, dy in [(1, 0), (0, 1), (1, 1), (-1, 1)]:
        shifted = np.roll(np.roll(gray, dy, axis=0), dx, axis=1)
        diff = np.abs(gray.astype(int) - shifted.astype(int))
        glcm_features.append(np.mean(diff))
    features.extend(glcm_features)
    
    # 4. 颜色特征
    for channel in range(3):
        channel_data = img_resized[:, :, channel]
        features.extend([
            np.mean(channel_data),
            np.std(channel_data)
        ])
    
    # 5. 形状特征
    # 使用轮廓检测
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        perimeter = cv2.arcLength(largest_contour, True)
        if perimeter > 0:
            circularity = 4 * np.pi * area / (perimeter * perimeter)
        else:
            circularity = 0
        features.extend([area / (64*64), perimeter / (64*4), circularity])
    else:
        features.extend([0, 0, 0])
    
    # 6. 频域特征（简化）
    fft = np.fft.fft2(gray)
    fft_magnitude = np.abs(fft)
    features.extend([
        np.mean(fft_magnitude),
        np.std(fft_magnitude)
    ])
    
    return np.array(features)

def load_and_augment_data(dataset_path, augmentation_factor=20):
    """加载数据并进行大量增强"""
    print(f"正在加载数据并进行 {augmentation_factor}x 增强...")
    
    class_names = sorted([d for d in os.listdir(dataset_path) 
                         if os.path.isdir(os.path.join(dataset_path, d))])
    print(f"发现类别: {class_names}")
    
    X, y = [], []
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_path, class_name)
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"处理类别 '{class_name}': {len(images)} 张原始图片")
        
        class_features = []
        class_labels = []
        
        for img_file in images:
            img_path = os.path.join(class_path, img_file)
            try:
                # 加载原始图像
                pil_img = Image.open(img_path).convert('RGB')
                
                # 提取原始图像特征
                original_features = extract_simple_features(pil_img)
                class_features.append(original_features)
                class_labels.append(class_idx)
                
                # 生成增强图像
                augmented_images = augment_image(pil_img, augmentation_factor)
                
                for aug_img in augmented_images:
                    aug_features = extract_simple_features(aug_img)
                    class_features.append(aug_features)
                    class_labels.append(class_idx)
                    
            except Exception as e:
                print(f"处理图片 {img_file} 时出错: {e}")
        
        X.extend(class_features)
        y.extend(class_labels)
        
        print(f"类别 '{class_name}' 增强后: {len(class_features)} 个样本")
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"数据增强完成: {len(X)} 个样本, {X.shape[1]} 个特征")
    
    # 打印类别分布
    unique, counts = np.unique(y, return_counts=True)
    for i, (class_idx, count) in enumerate(zip(unique, counts)):
        print(f"{class_names[class_idx]}: {count} 个样本")
    
    return X, y, class_names

def train_robust_model(X, y, class_names):
    """训练鲁棒模型"""
    print("开始训练模型...")
    
    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {len(X_train)} 个样本")
    print(f"测试集: {len(X_test)} 个样本")
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 使用随机森林（对小数据集效果好）
    model = RandomForestClassifier(
        n_estimators=500,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        bootstrap=True,
        random_state=42,
        class_weight='balanced',
        n_jobs=-1
    )
    
    # 训练模型
    print("训练随机森林模型...")
    model.fit(X_train_scaled, y_train)
    
    # 预测
    y_pred = model.predict(X_test_scaled)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 分类报告
    print("\n分类报告:")
    print(classification_report(y_test, y_pred, target_names=class_names))
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    
    # 绘制混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    annotations = []
    for i in range(cm.shape[0]):
        row = []
        for j in range(cm.shape[1]):
            row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
        annotations.append(row)
    
    sns.heatmap(
        cm_percent, 
        annot=annotations, 
        fmt='',
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={'label': '百分比 (%)'}
    )
    
    plt.title('数据增强+随机森林 - 混淆矩阵')
    plt.xlabel('预测类别')
    plt.ylabel('真实类别')
    plt.tight_layout()
    plt.savefig('augmented_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 特征重要性
    feature_importance = model.feature_importances_
    plt.figure(figsize=(12, 6))
    plt.bar(range(len(feature_importance)), feature_importance)
    plt.title('特征重要性')
    plt.xlabel('特征索引')
    plt.ylabel('重要性')
    plt.tight_layout()
    plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return model, scaler, accuracy

def main():
    """主函数"""
    print("="*60)
    print("数据增强+随机森林损伤分类器 - 目标准确率95%+")
    print("="*60)
    
    # 加载数据并进行大量增强
    X, y, class_names = load_and_augment_data("不同损伤类型图片", augmentation_factor=30)
    
    # 训练模型
    model, scaler, accuracy = train_robust_model(X, y, class_names)
    
    print(f"\n最终测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    if accuracy >= 0.95:
        print("🎉 恭喜！模型达到了95%以上的准确率目标！")
    elif accuracy >= 0.90:
        print("✅ 模型达到了90%以上的准确率，非常接近目标！")
    elif accuracy >= 0.80:
        print("✅ 模型达到了80%以上的准确率，这对于小数据集来说是很好的结果！")
    else:
        print(f"⚠️ 模型准确率为 {accuracy*100:.2f}%，未达到95%目标")
    
    # 保存模型
    import joblib
    joblib.dump(model, 'augmented_random_forest_model.pkl')
    joblib.dump(scaler, 'augmented_scaler.pkl')
    
    print("模型已保存")
    
    return model, accuracy

if __name__ == "__main__":
    model, accuracy = main()
