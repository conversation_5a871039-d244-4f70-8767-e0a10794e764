import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import ResNet50V2
import cv2
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

def load_data(dataset_path, img_size=(224, 224)):
    """加载数据"""
    print("正在加载数据...")
    
    class_names = sorted([d for d in os.listdir(dataset_path) 
                         if os.path.isdir(os.path.join(dataset_path, d))])
    print(f"发现类别: {class_names}")
    
    X, y = [], []
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_path, class_name)
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"加载类别 '{class_name}': {len(images)} 张图片")
        
        for img_file in images:
            img_path = os.path.join(class_path, img_file)
            try:
                # 使用PIL加载图像
                pil_img = Image.open(img_path)
                img = np.array(pil_img.convert('RGB'))
                img = cv2.resize(img, img_size)
                img = img.astype(np.float32) / 255.0
                
                X.append(img)
                y.append(class_idx)
            except Exception as e:
                print(f"处理图片 {img_file} 时出错: {e}")
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"数据加载完成: {len(X)} 张图片, {len(class_names)} 个类别")
    
    # 打印类别分布
    unique, counts = np.unique(y, return_counts=True)
    for i, (class_idx, count) in enumerate(zip(unique, counts)):
        print(f"{class_names[class_idx]}: {count} 张")
    
    return X, y, class_names

def create_model(num_classes, img_size=(224, 224)):
    """创建模型"""
    print("构建模型...")
    
    # 输入层
    inputs = layers.Input(shape=(*img_size, 3))
    
    # 预训练基础模型
    base_model = ResNet50V2(
        weights='imagenet',
        include_top=False,
        input_tensor=inputs
    )
    
    # 冻结预训练层
    base_model.trainable = False
    
    # 添加自定义分类头
    x = base_model.output
    x = layers.GlobalAveragePooling2D()(x)
    x = layers.Dropout(0.3)(x)
    x = layers.Dense(512, activation='relu')(x)
    x = layers.BatchNormalization()(x)
    x = layers.Dropout(0.5)(x)
    x = layers.Dense(256, activation='relu')(x)
    x = layers.BatchNormalization()(x)
    x = layers.Dropout(0.3)(x)
    outputs = layers.Dense(num_classes, activation='softmax')(x)
    
    model = models.Model(inputs, outputs)
    
    # 编译模型
    model.compile(
        optimizer=optimizers.Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print(f"模型参数总数: {model.count_params():,}")
    return model

def train_with_cross_validation(X, y, class_names, n_splits=5):
    """使用交叉验证训练模型"""
    print(f"开始 {n_splits} 折交叉验证训练...")
    
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
    cv_scores = []
    best_model = None
    best_score = 0
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        print(f"\n=== 第 {fold+1} 折 ===")
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # 创建模型
        model = create_model(len(class_names))
        
        # 计算类别权重
        class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(y_train),
            y=y_train
        )
        class_weight_dict = dict(enumerate(class_weights))
        
        # 数据增强
        train_datagen = ImageDataGenerator(
            rotation_range=30,
            width_shift_range=0.2,
            height_shift_range=0.2,
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            brightness_range=[0.8, 1.2],
            fill_mode='nearest'
        )
        
        val_datagen = ImageDataGenerator()
        
        train_generator = train_datagen.flow(X_train, y_train, batch_size=16, shuffle=True)
        val_generator = val_datagen.flow(X_val, y_val, batch_size=16, shuffle=False)
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # 训练模型
        history = model.fit(
            train_generator,
            steps_per_epoch=max(1, len(X_train) // 16),
            epochs=100,
            validation_data=val_generator,
            validation_steps=max(1, len(X_val) // 16),
            class_weight=class_weight_dict,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # 评估模型
        val_loss, val_accuracy = model.evaluate(X_val, y_val, verbose=0)
        cv_scores.append(val_accuracy)
        
        print(f"第 {fold+1} 折验证准确率: {val_accuracy:.4f}")
        
        # 保存最佳模型
        if val_accuracy > best_score:
            best_score = val_accuracy
            best_model = model
            best_model.save(f'best_model_fold_{fold+1}.h5')
    
    print(f"\n交叉验证结果:")
    print(f"平均准确率: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
    print(f"最佳准确率: {best_score:.4f}")
    
    return best_model, cv_scores

def evaluate_final_model(model, X_test, y_test, class_names):
    """评估最终模型"""
    print("评估最终模型...")
    
    # 预测
    y_pred_proba = model.predict(X_test, verbose=0)
    y_pred = np.argmax(y_pred_proba, axis=1)
    
    # 计算准确率
    accuracy = accuracy_score(y_test, y_pred)
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 分类报告
    print("\n分类报告:")
    print(classification_report(y_test, y_pred, target_names=class_names))
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    
    # 绘制混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    annotations = []
    for i in range(cm.shape[0]):
        row = []
        for j in range(cm.shape[1]):
            row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
        annotations.append(row)
    
    sns.heatmap(
        cm_percent, 
        annot=annotations, 
        fmt='',
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={'label': '百分比 (%)'}
    )
    
    plt.title('混淆矩阵')
    plt.xlabel('预测类别')
    plt.ylabel('真实类别')
    plt.tight_layout()
    plt.savefig('final_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return accuracy

def main():
    """主函数"""
    print("="*60)
    print("损伤类型图像分类器 - 目标准确率95%+")
    print("="*60)
    
    # 加载数据
    X, y, class_names = load_data("不同损伤类型图片")
    
    # 分割数据集 - 保留一部分作为最终测试
    X_train_val, X_test, y_train_val, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"\n数据集分割:")
    print(f"训练+验证集: {len(X_train_val)} 张")
    print(f"最终测试集: {len(X_test)} 张")
    
    # 交叉验证训练
    best_model, cv_scores = train_with_cross_validation(X_train_val, y_train_val, class_names)
    
    # 最终评估
    final_accuracy = evaluate_final_model(best_model, X_test, y_test, class_names)
    
    print(f"\n最终结果:")
    print(f"交叉验证平均准确率: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
    print(f"最终测试准确率: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
    
    if final_accuracy >= 0.95:
        print("🎉 恭喜！模型达到了95%以上的准确率目标！")
    else:
        print(f"⚠️ 模型准确率为 {final_accuracy*100:.2f}%，未达到95%目标")
    
    # 保存最终模型
    best_model.save('damage_classifier_final.h5')
    print("最终模型已保存为 'damage_classifier_final.h5'")
    
    return best_model, final_accuracy

if __name__ == "__main__":
    model, accuracy = main()
