import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
import cv2
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

def load_data(dataset_path, img_size=(128, 128)):
    """加载数据 - 使用较小的图像尺寸"""
    print("正在加载数据...")
    
    class_names = sorted([d for d in os.listdir(dataset_path) 
                         if os.path.isdir(os.path.join(dataset_path, d))])
    print(f"发现类别: {class_names}")
    
    X, y = [], []
    
    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(dataset_path, class_name)
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.tif', '.tiff', '.jpg', '.jpeg', '.png'))]
        
        print(f"加载类别 '{class_name}': {len(images)} 张图片")
        
        for img_file in images:
            img_path = os.path.join(class_path, img_file)
            try:
                # 使用PIL加载图像
                pil_img = Image.open(img_path)
                img = np.array(pil_img.convert('RGB'))
                img = cv2.resize(img, img_size)
                img = img.astype(np.float32) / 255.0
                
                X.append(img)
                y.append(class_idx)
            except Exception as e:
                print(f"处理图片 {img_file} 时出错: {e}")
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"数据加载完成: {len(X)} 张图片, {len(class_names)} 个类别")
    
    # 打印类别分布
    unique, counts = np.unique(y, return_counts=True)
    for i, (class_idx, count) in enumerate(zip(unique, counts)):
        print(f"{class_names[class_idx]}: {count} 张")
    
    return X, y, class_names

def create_lightweight_model(num_classes, img_size=(128, 128)):
    """创建轻量级模型 - 适合小数据集"""
    print("构建轻量级模型...")
    
    # 输入层
    inputs = layers.Input(shape=(*img_size, 3))
    
    # 使用MobileNetV2作为基础模型（更轻量）
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_tensor=inputs,
        alpha=0.75  # 减少模型复杂度
    )
    
    # 冻结大部分预训练层，只微调最后几层
    base_model.trainable = True
    for layer in base_model.layers[:-20]:
        layer.trainable = False
    
    # 添加简单的分类头
    x = base_model.output
    x = layers.GlobalAveragePooling2D()(x)
    x = layers.Dropout(0.2)(x)
    x = layers.Dense(128, activation='relu')(x)
    x = layers.Dropout(0.3)(x)
    outputs = layers.Dense(num_classes, activation='softmax')(x)
    
    model = models.Model(inputs, outputs)
    
    # 编译模型 - 使用较小的学习率
    model.compile(
        optimizer=optimizers.Adam(learning_rate=0.0001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print(f"模型参数总数: {model.count_params():,}")
    return model

def create_strong_augmentation():
    """创建强数据增强"""
    return ImageDataGenerator(
        rotation_range=40,
        width_shift_range=0.3,
        height_shift_range=0.3,
        shear_range=0.3,
        zoom_range=0.3,
        horizontal_flip=True,
        vertical_flip=True,
        brightness_range=[0.7, 1.3],
        channel_shift_range=0.2,
        fill_mode='nearest'
    )

def train_model_with_heavy_augmentation(X, y, class_names):
    """使用重度数据增强训练模型"""
    print("开始训练模型（重度数据增强）...")
    
    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"训练集: {len(X_train)} 张")
    print(f"验证集: {len(X_val)} 张")
    print(f"测试集: {len(X_test)} 张")
    
    # 创建模型
    model = create_lightweight_model(len(class_names))
    
    # 创建数据生成器 - 强数据增强
    train_datagen = create_strong_augmentation()
    val_datagen = ImageDataGenerator()
    
    # 增加每个epoch的步数来生成更多增强数据
    steps_per_epoch = max(50, len(X_train) * 3)  # 每个epoch生成3倍的增强数据
    
    train_generator = train_datagen.flow(
        X_train, y_train, 
        batch_size=8,  # 小批次
        shuffle=True
    )
    
    val_generator = val_datagen.flow(
        X_val, y_val, 
        batch_size=8, 
        shuffle=False
    )
    
    # 回调函数
    callbacks_list = [
        callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=20,
            restore_best_weights=True,
            verbose=1
        ),
        callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=10,
            min_lr=1e-8,
            verbose=1
        ),
        callbacks.ModelCheckpoint(
            'best_lightweight_model.h5',
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1
        )
    ]
    
    # 训练模型
    history = model.fit(
        train_generator,
        steps_per_epoch=steps_per_epoch,
        epochs=150,  # 更多epochs
        validation_data=val_generator,
        validation_steps=max(1, len(X_val) // 8),
        callbacks=callbacks_list,
        verbose=1
    )
    
    return model, history, X_test, y_test

def evaluate_model(model, X_test, y_test, class_names):
    """评估模型"""
    print("评估模型性能...")
    
    # 预测
    y_pred_proba = model.predict(X_test, verbose=0)
    y_pred = np.argmax(y_pred_proba, axis=1)
    
    # 计算准确率
    accuracy = accuracy_score(y_test, y_pred)
    print(f"测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 分类报告
    print("\n分类报告:")
    print(classification_report(y_test, y_pred, target_names=class_names))
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    
    # 绘制混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    annotations = []
    for i in range(cm.shape[0]):
        row = []
        for j in range(cm.shape[1]):
            row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
        annotations.append(row)
    
    sns.heatmap(
        cm_percent, 
        annot=annotations, 
        fmt='',
        cmap='Blues',
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={'label': '百分比 (%)'}
    )
    
    plt.title('混淆矩阵')
    plt.xlabel('预测类别')
    plt.ylabel('真实类别')
    plt.tight_layout()
    plt.savefig('optimized_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return accuracy

def plot_training_history(history):
    """绘制训练历史"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 准确率
    ax1.plot(history.history['accuracy'], label='训练准确率')
    ax1.plot(history.history['val_accuracy'], label='验证准确率')
    ax1.set_title('模型准确率')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('准确率')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 损失
    ax2.plot(history.history['loss'], label='训练损失')
    ax2.plot(history.history['val_loss'], label='验证损失')
    ax2.set_title('模型损失')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('损失')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimized_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("="*60)
    print("优化的损伤类型图像分类器 - 目标准确率95%+")
    print("="*60)
    
    # 加载数据
    X, y, class_names = load_data("不同损伤类型图片", img_size=(128, 128))
    
    # 训练模型
    model, history, X_test, y_test = train_model_with_heavy_augmentation(X, y, class_names)
    
    # 绘制训练历史
    plot_training_history(history)
    
    # 评估模型
    accuracy = evaluate_model(model, X_test, y_test, class_names)
    
    print(f"\n最终测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    if accuracy >= 0.95:
        print("🎉 恭喜！模型达到了95%以上的准确率目标！")
    else:
        print(f"⚠️ 模型准确率为 {accuracy*100:.2f}%，未达到95%目标")
        print("建议：")
        print("1. 收集更多训练数据")
        print("2. 尝试集成学习方法")
        print("3. 使用更强的数据增强")
    
    # 保存最终模型
    model.save('optimized_damage_classifier.h5')
    print("最终模型已保存为 'optimized_damage_classifier.h5'")
    
    return model, accuracy

if __name__ == "__main__":
    model, accuracy = main()
